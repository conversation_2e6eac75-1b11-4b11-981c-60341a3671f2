<?php

namespace App\Listeners;

use App\Events\ReturnRequestCreated;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Models\User;

class SendReturnRequestEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ReturnRequestCreated  $event
     * @return void
     */
    public function handle(ReturnRequestCreated $event)
    {
        $order = $event->order;
        $returns = $event->returns;
        $reason = $event->reason;

        // Send confirmation email to customer
        $customerData = [
            'locale' => app()->getLocale(),
            'subject' => 'Return Request Received - Order #' . $order->order_number,
            'order' => $order,
            'returns' => $returns,
            'reason' => $reason,
            'view_file' => 'backend.emails.return_request_customer',
            'priority' => 'high'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($customerData, 'high'));

        // Send notification to salesman if assigned
        if ($order->salesman_id) {
            $salesmanData = [
                'locale' => app()->getLocale(),
                'subject' => 'Return Request - Order #' . $order->order_number,
                'order' => $order,
                'returns' => $returns,
                'reason' => $reason,
                'view_file' => 'backend.emails.salesman-return-request',
                'priority' => 'high'
            ];

            $salesman = $order->salesman;
            if ($salesman) {
                Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                    ->queue(new MailQueue($salesmanData, 'high'));
            }
        }

        // Send notification to admins
        $adminData = [
            'locale' => app()->getLocale(),
            'subject' => 'New Return Request - Order #' . $order->order_number,
            'order' => $order,
            'returns' => $returns,
            'reason' => $reason,
            'view_file' => 'backend.emails.return_request_admin',
            'priority' => 'high'
        ];

        $admins = User::where('role', 'admin')->get();
        foreach ($admins as $admin) {
            Mail::to($admin->email, $admin->first_name . ' ' . $admin->last_name)
                ->queue(new MailQueue($adminData, 'high'));
        }
    }
}
