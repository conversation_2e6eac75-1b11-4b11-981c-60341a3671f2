<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Order Events
        \App\Events\OrderCreated::class => [
            \App\Listeners\SendOrderCreatedEmail::class,
        ],

        \App\Events\OrderStatusChanged::class => [
            \App\Listeners\SendOrderStatusChangedEmail::class,
        ],

        \App\Events\OrderAssignedToSalesman::class => [
            \App\Listeners\SendOrderAssignmentEmail::class,
        ],

        // User Events
        \App\Events\UserAssignedToSalesman::class => [
            \App\Listeners\SendUserAssignmentEmail::class,
        ],

        // Return Events
        \App\Events\ReturnRequestCreated::class => [
            \App\Listeners\SendReturnRequestEmail::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
