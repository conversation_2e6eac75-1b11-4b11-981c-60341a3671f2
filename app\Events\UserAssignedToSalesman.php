<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserAssignedToSalesman
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $customer;
    public $salesman;
    public $previousSalesman;

    /**
     * Create a new event instance.
     *
     * @param User $customer
     * @param User $salesman
     * @param User|null $previousSalesman
     * @return void
     */
    public function __construct(User $customer, User $salesman, User $previousSalesman = null)
    {
        $this->customer = $customer;
        $this->salesman = $salesman;
        $this->previousSalesman = $previousSalesman;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
