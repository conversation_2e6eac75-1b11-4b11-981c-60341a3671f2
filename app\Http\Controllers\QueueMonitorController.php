<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class QueueMonitorController extends Controller
{
    /**
     * Show queue monitoring dashboard
     */
    public function index()
    {
        $stats = $this->getQueueStats();
        $recentJobs = $this->getRecentJobs();
        $failedJobs = $this->getFailedJobs();
        
        return view('backend.queue-monitor.index', compact('stats', 'recentJobs', 'failedJobs'));
    }

    /**
     * Get queue statistics
     */
    public function getStats()
    {
        $stats = $this->getQueueStats();
        return response()->json($stats);
    }

    /**
     * Get failed jobs
     */
    public function getFailedJobs()
    {
        $failedJobs = DB::table('failed_jobs')
            ->orderBy('failed_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function ($job) {
                $payload = json_decode($job->payload, true);
                return [
                    'id' => $job->id,
                    'uuid' => $job->uuid,
                    'connection' => $job->connection,
                    'queue' => $job->queue,
                    'payload' => $payload,
                    'exception' => $job->exception,
                    'failed_at' => Carbon::parse($job->failed_at),
                    'job_class' => $payload['displayName'] ?? 'Unknown'
                ];
            });

        return response()->json($failedJobs);
    }

    /**
     * Retry failed job
     */
    public function retryFailedJob(Request $request, $id)
    {
        try {
            Artisan::call('queue:retry', ['id' => $id]);
            
            Log::info('Failed job retried', ['job_id' => $id]);
            
            return response()->json([
                'success' => true,
                'message' => 'Job queued for retry successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retry job', [
                'job_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retry job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete failed job
     */
    public function deleteFailedJob(Request $request, $id)
    {
        try {
            DB::table('failed_jobs')->where('id', $id)->delete();
            
            Log::info('Failed job deleted', ['job_id' => $id]);
            
            return response()->json([
                'success' => true,
                'message' => 'Failed job deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete job', [
                'job_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Flush all failed jobs
     */
    public function flushFailedJobs()
    {
        try {
            Artisan::call('queue:flush');
            
            Log::info('All failed jobs flushed');
            
            return response()->json([
                'success' => true,
                'message' => 'All failed jobs have been deleted'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to flush jobs', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to flush jobs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get queue worker status
     */
    public function getWorkerStatus()
    {
        // This is a simplified check - in production you might want to use
        // a more sophisticated method to check if workers are running
        $workers = $this->checkWorkerProcesses();
        
        return response()->json([
            'workers_running' => count($workers) > 0,
            'worker_count' => count($workers),
            'workers' => $workers
        ]);
    }

    /**
     * Get detailed queue statistics
     */
    private function getQueueStats()
    {
        // Pending jobs by queue
        $pendingJobs = DB::table('jobs')
            ->select('queue', DB::raw('count(*) as count'))
            ->groupBy('queue')
            ->get()
            ->pluck('count', 'queue')
            ->toArray();

        // Failed jobs count
        $failedJobsCount = DB::table('failed_jobs')->count();

        // Jobs processed today (estimate based on job IDs)
        $jobsToday = $this->estimateJobsProcessedToday();

        // Average processing time (simplified calculation)
        $avgProcessingTime = $this->calculateAverageProcessingTime();

        return [
            'pending_jobs' => array_sum($pendingJobs),
            'pending_by_queue' => $pendingJobs,
            'failed_jobs' => $failedJobsCount,
            'jobs_today' => $jobsToday,
            'avg_processing_time' => $avgProcessingTime,
            'last_updated' => now()->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Get recent jobs (simplified - shows pending jobs)
     */
    private function getRecentJobs($limit = 20)
    {
        return DB::table('jobs')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($job) {
                $payload = json_decode($job->payload, true);
                return [
                    'id' => $job->id,
                    'queue' => $job->queue,
                    'attempts' => $job->attempts,
                    'created_at' => Carbon::createFromTimestamp($job->created_at),
                    'available_at' => Carbon::createFromTimestamp($job->available_at),
                    'job_class' => $payload['displayName'] ?? 'Unknown'
                ];
            });
    }

    /**
     * Estimate jobs processed today
     */
    private function estimateJobsProcessedToday()
    {
        // This is a simplified estimation
        // In a real application, you might want to track this more accurately
        $startOfDay = Carbon::today()->timestamp;
        $currentJobId = DB::table('jobs')->max('id') ?? 0;
        $startOfDayJobId = DB::table('jobs')->where('created_at', '>=', $startOfDay)->min('id') ?? $currentJobId;
        
        return max(0, $currentJobId - $startOfDayJobId);
    }

    /**
     * Calculate average processing time
     */
    private function calculateAverageProcessingTime()
    {
        // This is a placeholder - implement based on your job tracking needs
        return '2.3s';
    }

    /**
     * Check for running worker processes
     */
    private function checkWorkerProcesses()
    {
        // This is a simplified check for Unix-like systems
        // Adjust based on your deployment environment
        $workers = [];
        
        if (function_exists('shell_exec') && !in_array('shell_exec', explode(',', ini_get('disable_functions')))) {
            $output = shell_exec('ps aux | grep "queue:work" | grep -v grep');
            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (!empty($line)) {
                        $workers[] = [
                            'process' => $line,
                            'status' => 'running'
                        ];
                    }
                }
            }
        }
        
        return $workers;
    }

    /**
     * Clear all jobs from a specific queue
     */
    public function clearQueue(Request $request)
    {
        $request->validate([
            'queue' => 'required|string'
        ]);

        $queue = $request->queue;

        try {
            $deletedCount = DB::table('jobs')->where('queue', $queue)->delete();
            
            Log::info('Queue cleared', [
                'queue' => $queue,
                'deleted_count' => $deletedCount
            ]);
            
            return response()->json([
                'success' => true,
                'message' => "Cleared {$deletedCount} jobs from queue '{$queue}'"
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to clear queue', [
                'queue' => $queue,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear queue: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get queue health check
     */
    public function healthCheck()
    {
        $stats = $this->getQueueStats();
        $workers = $this->checkWorkerProcesses();
        
        $health = [
            'status' => 'healthy',
            'issues' => []
        ];

        // Check if workers are running
        if (count($workers) === 0) {
            $health['status'] = 'warning';
            $health['issues'][] = 'No queue workers detected';
        }

        // Check for too many failed jobs
        if ($stats['failed_jobs'] > 50) {
            $health['status'] = 'warning';
            $health['issues'][] = 'High number of failed jobs (' . $stats['failed_jobs'] . ')';
        }

        // Check for too many pending jobs
        if ($stats['pending_jobs'] > 1000) {
            $health['status'] = 'critical';
            $health['issues'][] = 'High number of pending jobs (' . $stats['pending_jobs'] . ')';
        }

        return response()->json($health);
    }
}
