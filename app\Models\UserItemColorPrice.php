<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserItemColorPrice extends Model
{
    use HasFactory;

    protected $table = 'user_item_color_prices';

    protected $fillable = ['user_id', 'item_color_id', 'custom_price'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function itemColor()
    {
        return $this->belongsTo(ItemColor::class);
    }
}
