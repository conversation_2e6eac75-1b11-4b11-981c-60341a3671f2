<?php

namespace App\Listeners;

use App\Events\UserStatusChanged;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendUserStatusChangedEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserStatusChanged  $event
     * @return void
     */
    public function handle(UserStatusChanged $event)
    {
        $user = $event->user;
        $oldStatus = $event->oldStatus;

        $data = [
            'user' => $user,
            'old_status' => $oldStatus,
        ];

        Mail::to($user->email)->queue(new MailQueue(
            'backend.emails.user_status_changed',
            $data,
            'Account Status Update - Lamart Manufacturing'
        ));
    }
}
