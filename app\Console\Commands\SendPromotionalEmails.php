<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Http\Controllers\EmailController;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SendPromotionalEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-promotional 
                            {--type=general : Type of promotional email (general, category, product)}
                            {--category= : Category ID for category-specific promotions}
                            {--product= : Product ID for product-specific promotions}
                            {--customer-role=customer : Customer role to target}
                            {--dry-run : Show what would be sent without actually sending}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send promotional emails to customers based on various criteria';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->option('type');
        $categoryId = $this->option('category');
        $productId = $this->option('product');
        $customerRole = $this->option('customer-role');
        $dryRun = $this->option('dry-run');

        $this->info("Preparing promotional email campaign...");
        $this->info("Type: {$type}");

        try {
            // Get target customers
            $customers = $this->getTargetCustomers($customerRole);
            
            if ($customers->isEmpty()) {
                $this->warn("No customers found matching the criteria");
                return 0;
            }

            $this->info("Found {$customers->count()} target customers");

            // Prepare email data based on type
            $emailData = $this->prepareEmailData($type, $categoryId, $productId);

            if (!$emailData) {
                $this->error("Failed to prepare email data");
                return 1;
            }

            $sentCount = 0;
            $errorCount = 0;

            // Send emails
            foreach ($customers as $customer) {
                try {
                    if (!$dryRun) {
                        $this->sendPromotionalEmail($customer, $emailData);
                    }
                    
                    $sentCount++;
                    $this->line("Prepared email for: {$customer->first_name} {$customer->last_name} ({$customer->email})");
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("Failed to send email to {$customer->email}: " . $e->getMessage());
                    Log::error('Failed to send promotional email', [
                        'customer_id' => $customer->id,
                        'customer_email' => $customer->email,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->info("\n=== Campaign Summary ===");
            $this->info("Emails prepared: {$sentCount}");
            if ($errorCount > 0) {
                $this->error("Errors encountered: {$errorCount}");
            }

            if ($dryRun) {
                $this->warn("DRY RUN - No emails were actually sent");
            }

            Log::info('Promotional email campaign completed', [
                'type' => $type,
                'sent_count' => $sentCount,
                'error_count' => $errorCount,
                'dry_run' => $dryRun
            ]);

            return $errorCount > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $this->error("Campaign failed: " . $e->getMessage());
            Log::error('Promotional email campaign failed', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    /**
     * Get target customers based on role
     */
    private function getTargetCustomers($role)
    {
        return User::where('role', $role)
            ->where('status', 'active')
            ->whereNotNull('email')
            ->where('email_verified_at', '!=', null)
            ->get();
    }

    /**
     * Prepare email data based on type
     */
    private function prepareEmailData($type, $categoryId = null, $productId = null)
    {
        $baseData = [
            'subject' => 'Special Offer from Lamart Manufacturing',
            'preheader' => 'Don\'t miss out on this exclusive offer!',
            'headline' => 'Special Offer Just for You!',
            'subheadline' => 'Limited time offer - don\'t miss out!',
            'cta_text' => 'Shop Now',
            'cta_url' => 'https://lamartmfg.com',
            'expiry_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
        ];

        switch ($type) {
            case 'category':
                if ($categoryId) {
                    $category = Category::find($categoryId);
                    if ($category) {
                        $baseData['headline'] = "Special Offer on {$category->title}";
                        $baseData['cta_url'] = "https://lamartmfg.com/category/{$category->slug}";
                        $baseData['featured_products'] = $this->getFeaturedProductsByCategory($categoryId);
                    }
                }
                break;

            case 'product':
                if ($productId) {
                    $product = Product::find($productId);
                    if ($product) {
                        $baseData['headline'] = "Special Offer on {$product->title}";
                        $baseData['cta_url'] = "https://lamartmfg.com/product/{$product->slug}";
                        $baseData['featured_products'] = [$this->formatProductForEmail($product)];
                    }
                }
                break;

            case 'general':
            default:
                $baseData['featured_products'] = $this->getFeaturedProducts();
                break;
        }

        return $baseData;
    }

    /**
     * Get featured products by category
     */
    private function getFeaturedProductsByCategory($categoryId, $limit = 3)
    {
        $products = Product::where('cat_id', $categoryId)
            ->where('status', 'active')
            ->where('is_featured', 1)
            ->limit($limit)
            ->get();

        return $products->map(function ($product) {
            return $this->formatProductForEmail($product);
        })->toArray();
    }

    /**
     * Get general featured products
     */
    private function getFeaturedProducts($limit = 4)
    {
        $products = Product::where('status', 'active')
            ->where('is_featured', 1)
            ->limit($limit)
            ->get();

        return $products->map(function ($product) {
            return $this->formatProductForEmail($product);
        })->toArray();
    }

    /**
     * Format product data for email
     */
    private function formatProductForEmail($product)
    {
        return [
            'name' => $product->title,
            'description' => $product->summary,
            'price' => $product->price,
            'original_price' => $product->price > $product->offer_price ? $product->price : null,
            'image' => $product->photo ? asset('storage/' . $product->photo) : null,
            'url' => "https://lamartmfg.com/product/{$product->slug}"
        ];
    }

    /**
     * Send promotional email to customer
     */
    private function sendPromotionalEmail($customer, $emailData)
    {
        $emailController = new EmailController();
        
        $request = new \Illuminate\Http\Request();
        $request->merge([
            'recipients' => [$customer->email],
            'subject' => $emailData['subject'],
            'template_data' => $emailData,
            'priority' => 'normal'
        ]);

        $response = $emailController->sendPromotionalEmail($request);
        
        if ($response->getStatusCode() !== 200) {
            throw new \Exception('Email controller returned error status');
        }
    }
}
