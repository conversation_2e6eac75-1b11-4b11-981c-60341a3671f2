<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;
use App\Mail\MailQueue;
use App\Models\User;
use App\Models\Order;
use App\Events\OrderCreated;
use App\Events\OrderStatusChanged;
use App\Events\OrderAssignedToSalesman;
use App\Events\UserAssignedToSalesman;
use App\Events\ReturnRequestCreated;
use Exception;

class EmailController extends Controller
{
    /**
     * Send registration email to new user
     */
    public function sendNewRegistrationEmail($data)
    {
        try {
            $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
            $data['subject'] = 'Registration Successful';
            $data['view_file'] = 'emails.registered';
            $data['priority'] = 'high';

            Mail::to($data['email'], $data['name'])->queue(new MailQueue($data, 'high'));

            Log::info('Registration email queued successfully', ['email' => $data['email']]);
            return response()->json(['message' => 'Email sent successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to send registration email', [
                'email' => $data['email'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to send email.'], 500);
        }
    }

    /**
     * Send registration notification to admin
     */
    public function sendNewRegistrationEmailAdmin($data)
    {
        try {
            $data['subject'] = 'New User Registered';
            $data['view_file'] = 'emails.admin.registered';
            $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
            $data['priority'] = 'normal';

            if (defined('ADMIN_EMAILS')) {
                Mail::to(ADMIN_EMAILS)->queue(new MailQueue($data));
            } else {
                $admins = User::where('role', 'admin')->get();
                foreach ($admins as $admin) {
                    Mail::to($admin->email, $admin->first_name . ' ' . $admin->last_name)
                        ->queue(new MailQueue($data));
                }
            }

            Log::info('Admin registration notification queued successfully');
        } catch (Exception $e) {
            Log::error('Failed to send admin registration email', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send admin-created account email
     */
    public function sendAdminNewRegistrationEmail($data)
    {
        try {
            $data['subject'] = 'Admin has invited you to ' . env('APP_NAME');
            $data['view_file'] = 'emails.created';
            $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
            $data['priority'] = 'high';

            Mail::to($data['email'], $data['name'])->queue(new MailQueue($data, 'high'));

            Log::info('Admin-created account email queued successfully', ['email' => $data['email']]);
        } catch (Exception $e) {
            Log::error('Failed to send admin-created account email', [
                'email' => $data['email'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send user status change notification
     */
    public function userStatusChanged($data)
    {
        try {
            $data['subject'] = 'Account Status Changed';
            $data['view_file'] = 'emails.user_status';
            $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
            $data['priority'] = 'high';

            Mail::to($data['email'], $data['name'])->queue(new MailQueue($data, 'high'));

            Log::info('User status change email queued successfully', ['email' => $data['email']]);
        } catch (Exception $e) {
            Log::error('Failed to send user status change email', [
                'email' => $data['email'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send visit reminder to salesman
     */
    public function send_visit_reminders_salesman($reminder)
    {
        try {
            $data = [
                'locale' => App::getLocale(),
                'subject' => 'Visit Reminder',
                'reminder' => $reminder,
                'view_file' => 'backend.emails.salesman-visit-reminder',
                'priority' => 'normal'
            ];

            Mail::to($reminder->salesman->email, $reminder->salesman->first_name . ' ' . $reminder->salesman->last_name)
                ->queue(new MailQueue($data));

            Log::info('Visit reminder email queued successfully', [
                'salesman_id' => $reminder->salesman_id,
                'customer_id' => $reminder->customer_id
            ]);

            return response()->json(['message' => 'Email sent successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to send visit reminder email', [
                'salesman_id' => $reminder->salesman_id ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to send email.'], 500);
        }
    }

    /**
     * Send return request emails (deprecated - use event instead)
     * @deprecated Use ReturnRequestCreated event instead
     */
    public function send_return($order, $returns, $reason)
    {
        try {
            // Fire event instead of sending emails directly
            Event::dispatch(new ReturnRequestCreated($order, $returns, $reason));

            Log::info('Return request event dispatched successfully', ['order_id' => $order->id]);
            return response()->json(['message' => 'Emails queued successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to dispatch return request event', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to process return request.'], 500);
        }
    }

    /**
     * Send order details (deprecated - use event instead)
     * @deprecated Use OrderCreated event instead
     */
    public function sendOrderDetails($order)
    {
        try {
            // Fire event instead of sending emails directly
            if ($order instanceof Order) {
                Event::dispatch(new OrderCreated($order));
            } else {
                // Legacy support for array data
                $data = [
                    'locale' => App::getLocale(),
                    'subject' => 'New Order Details',
                    'order' => $order,
                    'view_file' => 'backend.emails.order_details_customer',
                    'priority' => 'high'
                ];

                Mail::to($order['email'], $order['first_name'] . ' ' . $order['last_name'])
                    ->queue(new MailQueue($data, 'high'));
            }

            Log::info('Order details email queued successfully', [
                'order_id' => is_array($order) ? ($order['id'] ?? 'unknown') : $order->id
            ]);

            return response()->json(['message' => 'Email sent successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to send order details email', [
                'order_id' => is_array($order) ? ($order['id'] ?? 'unknown') : ($order->id ?? 'unknown'),
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to send email.'], 500);
        }
    }

    /**
     * Trigger order created event
     */
    public function triggerOrderCreated(Order $order)
    {
        try {
            Event::dispatch(new OrderCreated($order));
            Log::info('OrderCreated event dispatched', ['order_id' => $order->id]);
            return response()->json(['message' => 'Order created event triggered successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to dispatch OrderCreated event', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to trigger order created event.'], 500);
        }
    }

    /**
     * Trigger order status changed event
     */
    public function triggerOrderStatusChanged(Order $order, $oldStatus, $newStatus)
    {
        try {
            Event::dispatch(new OrderStatusChanged($order, $oldStatus, $newStatus));
            Log::info('OrderStatusChanged event dispatched', [
                'order_id' => $order->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);
            return response()->json(['message' => 'Order status changed event triggered successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to dispatch OrderStatusChanged event', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to trigger order status changed event.'], 500);
        }
    }

    /**
     * Trigger order assigned to salesman event
     */
    public function triggerOrderAssignedToSalesman(Order $order, User $salesman, User $previousSalesman = null)
    {
        try {
            Event::dispatch(new OrderAssignedToSalesman($order, $salesman, $previousSalesman));
            Log::info('OrderAssignedToSalesman event dispatched', [
                'order_id' => $order->id,
                'salesman_id' => $salesman->id,
                'previous_salesman_id' => $previousSalesman ? $previousSalesman->id : null
            ]);
            return response()->json(['message' => 'Order assignment event triggered successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to dispatch OrderAssignedToSalesman event', [
                'order_id' => $order->id,
                'salesman_id' => $salesman->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to trigger order assignment event.'], 500);
        }
    }

    /**
     * Trigger user assigned to salesman event
     */
    public function triggerUserAssignedToSalesman(User $customer, User $salesman, User $previousSalesman = null)
    {
        try {
            Event::dispatch(new UserAssignedToSalesman($customer, $salesman, $previousSalesman));
            Log::info('UserAssignedToSalesman event dispatched', [
                'customer_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'previous_salesman_id' => $previousSalesman ? $previousSalesman->id : null
            ]);
            return response()->json(['message' => 'User assignment event triggered successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to dispatch UserAssignedToSalesman event', [
                'customer_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to trigger user assignment event.'], 500);
        }
    }

    /**
     * Send promotional email
     */
    public function sendPromotionalEmail(Request $request)
    {
        try {
            $validated = $request->validate([
                'recipients' => 'required|array',
                'recipients.*' => 'email',
                'subject' => 'required|string|max:255',
                'template_data' => 'required|array',
                'priority' => 'in:high,normal,low'
            ]);

            $templateData = $validated['template_data'];
            $templateData['subject'] = $validated['subject'];
            $templateData['view_file'] = 'backend.emails.promotional_email';
            $templateData['priority'] = $validated['priority'] ?? 'normal';

            $queue = $templateData['priority'] === 'high' ? 'high' : 'emails';

            foreach ($validated['recipients'] as $email) {
                Mail::to($email)->queue(new MailQueue($templateData, $queue));
            }

            Log::info('Promotional emails queued successfully', [
                'recipient_count' => count($validated['recipients']),
                'subject' => $validated['subject']
            ]);

            return response()->json([
                'message' => 'Promotional emails queued successfully.',
                'recipient_count' => count($validated['recipients'])
            ], 200);
        } catch (Exception $e) {
            Log::error('Failed to send promotional emails', [
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to send promotional emails.'], 500);
        }
    }

    /**
     * Send test email
     */
    public function sendTestEmail(Request $request)
    {
        try {
            $validated = $request->validate([
                'email' => 'required|email',
                'template' => 'required|string',
                'test_data' => 'array'
            ]);

            $data = array_merge($validated['test_data'] ?? [], [
                'subject' => 'Test Email - ' . ucfirst(str_replace(['_', '-'], ' ', $validated['template'])),
                'view_file' => $validated['template'],
                'priority' => 'high'
            ]);

            Mail::to($validated['email'])->queue(new MailQueue($data, 'high'));

            Log::info('Test email queued successfully', [
                'email' => $validated['email'],
                'template' => $validated['template']
            ]);

            return response()->json(['message' => 'Test email sent successfully.'], 200);
        } catch (Exception $e) {
            Log::error('Failed to send test email', [
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Failed to send test email.'], 500);
        }
    }
}
