# 🧪 Email System Testing Guide

## Prerequisites

### 1. Environment Setup
```bash
# Ensure your .env has queue configuration
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Lamart Manufacturing"
```

### 2. Database Setup
```bash
# Run migrations to create queue tables
php artisan migrate

# Clear any cached config
php artisan config:clear
php artisan cache:clear
```

## 🚀 Quick Start Testing

### Step 1: Start Queue Worker
```bash
# In a separate terminal, start the queue worker
php artisan queue:work database --queue=high,emails,default --sleep=3 --tries=3 --timeout=60
```

### Step 2: Run Interactive Testing Tool
```bash
# Run the comprehensive testing command
php artisan email:test-system
```

This will show you an interactive menu:
```
🧪 Email System Testing Tool
================================
What would you like to test?
  [0] Create test data
  [1] Test all email events  
  [2] Test complete order flow
  [3] Check queue status
  [4] View email templates
  [5] Exit
```

### Step 3: Create Test Data
Choose option `0` to create:
- Test customer (<EMAIL>)
- Test salesman (<EMAIL>) 
- Test admin (<EMAIL>)
- Test order with sample data

### Step 4: Test Email Events
Choose option `1` to test all email events:
- OrderCreated
- OrderStatusChanged
- OrderAssignedToSalesman
- UserAssignedToSalesman
- ReturnRequestCreated

## 📧 Email Template Testing

### Access Email Testing Dashboard
1. **Login as admin** to your Laravel application
2. **Navigate to**: `/email-testing`
3. **Preview templates** by clicking "Preview" buttons
4. **Send test emails** by clicking "Test" buttons

### Available Templates to Test:

#### Order Emails
- ✅ `backend.emails.order_details_customer` - Order confirmation
- ✅ `backend.emails.order_assigned_salesman` - Salesman assignment
- ✅ `backend.emails.salesman_assigned_customer` - Customer notification
- ✅ `backend.emails.order_status_changed_customer` - Status updates
- ✅ `backend.emails.order_picker_notification` - Picker notifications

#### User Management
- ✅ `emails.registered` - User registration
- ✅ `emails.created` - Admin-created accounts
- ✅ `emails.user_status` - Status changes
- ✅ `backend.emails.user_assigned_salesman_customer` - User assignments

#### Other Templates
- ✅ `backend.emails.salesman-visit-reminder` - Visit reminders
- ✅ `backend.emails.salesman-return-request` - Return requests
- ✅ `backend.emails.promotional_email` - Marketing campaigns

## 🔄 Real Order Flow Testing

### Test Order Creation
1. **Go to**: `/admin/orders/create`
2. **Create a new order** with test data
3. **Check queue**: `php artisan queue:work` should process OrderCreated event
4. **Verify emails sent** to customer, admin, salesman, and picker

### Test Status Changes
1. **Go to**: `/admin/orders`
2. **Edit an existing order**
3. **Change status** from "new" → "process" → "delivered"
4. **Check queue** for OrderStatusChanged events
5. **Verify status change emails** sent to customer and salesman

### Test Salesman Assignment
1. **Edit an order**
2. **Change the assigned salesman**
3. **Check queue** for OrderAssignedToSalesman event
4. **Verify emails** sent to new salesman, customer, and previous salesman

## 📊 Queue Monitoring

### Access Queue Monitor Dashboard
1. **Navigate to**: `/queue-monitor`
2. **Monitor real-time stats**:
   - Pending jobs
   - Failed jobs
   - Jobs processed today
   - Average processing time

### Queue Management Commands
```bash
# Check queue status
php artisan queue:monitor database:default,database:high,database:emails

# Retry failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush

# Restart queue workers
php artisan queue:restart
```

## 🧪 Manual Testing Scenarios

### Scenario 1: New Customer Order
```bash
# 1. Create test data
php artisan email:test-system --create-test-data

# 2. Start queue worker
php artisan queue:work database --queue=high,emails,default

# 3. Create order via admin panel
# 4. Check emails sent to:
#    - Customer (order confirmation)
#    - Admin (new order notification)
#    - Salesman (order assignment)
#    - Picker (picking notification)
```

### Scenario 2: Order Status Updates
```bash
# 1. Change order status to "process"
# 2. Check customer receives status update email
# 3. Change status to "delivered"  
# 4. Check customer receives delivery confirmation
```

### Scenario 3: Return Request
```bash
# 1. Create return request for an order
# 2. Check emails sent to:
#    - Customer (return confirmation)
#    - Salesman (return notification)
#    - Admin (return alert)
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Emails Not Sending
```bash
# Check queue worker is running
ps aux | grep "queue:work"

# Check failed jobs
php artisan queue:failed

# Check logs
tail -f storage/logs/laravel.log
```

#### 2. Template Errors
- **Preview templates** at `/email-testing` to check for errors
- **Check logs** for Blade template errors
- **Verify data** is being passed correctly to templates

#### 3. Queue Issues
```bash
# Restart queue workers
php artisan queue:restart

# Clear queue cache
php artisan cache:clear

# Check database connections
php artisan tinker
>>> DB::connection()->getPdo()
```

### Debug Commands
```bash
# Test specific event
php artisan tinker
>>> event(new App\Events\OrderCreated(App\Models\Order::first()))

# Check queue tables
php artisan tinker
>>> DB::table('jobs')->count()
>>> DB::table('failed_jobs')->count()

# Test email configuration
php artisan tinker
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); })
```

## ✅ Testing Checklist

### Email Templates
- [ ] All templates preview correctly
- [ ] Mobile responsiveness works
- [ ] All links are functional
- [ ] Branding is consistent
- [ ] Unsubscribe links work

### Email Events
- [ ] OrderCreated triggers all required emails
- [ ] OrderStatusChanged sends appropriate notifications
- [ ] OrderAssignedToSalesman notifies all parties
- [ ] UserAssignedToSalesman works correctly
- [ ] ReturnRequestCreated processes properly

### Queue System
- [ ] Queue workers process jobs
- [ ] Failed jobs are handled gracefully
- [ ] Priority queues work (high > emails > default)
- [ ] Queue monitoring dashboard functions
- [ ] Error logging is comprehensive

### Integration
- [ ] Order creation triggers events
- [ ] Status changes trigger events
- [ ] Admin panel integration works
- [ ] Email testing dashboard accessible
- [ ] Queue monitor dashboard accessible

## 🎯 Performance Testing

### Load Testing
```bash
# Create multiple test orders
for i in {1..10}; do
    php artisan email:test-system --test-events
done

# Monitor queue processing
watch -n 1 'php artisan queue:monitor database:default,database:emails,database:high'
```

### Email Volume Testing
```bash
# Test promotional email system
php artisan email:send-promotional --type=general --dry-run

# Test visit reminders
php artisan email:send-visit-reminders --dry-run
```

## 📈 Success Metrics

### What to Measure
- **Email delivery rate** (should be >95%)
- **Queue processing time** (should be <30 seconds)
- **Failed job rate** (should be <5%)
- **Template rendering time** (should be <2 seconds)

### Monitoring Tools
- Queue Monitor Dashboard (`/queue-monitor`)
- Email Testing Dashboard (`/email-testing`)
- Laravel logs (`storage/logs/laravel.log`)
- Queue worker logs

## 🎉 Next Steps

Once testing is complete:
1. **Deploy to staging** environment
2. **Test with real email addresses**
3. **Configure production queue workers**
4. **Set up monitoring alerts**
5. **Train team** on new email system
6. **Document any customizations**

Happy testing! 🚀
