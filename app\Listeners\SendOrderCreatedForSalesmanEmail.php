<?php

namespace App\Listeners;

use App\Events\OrderCreatedForSalesman;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderCreatedForSalesmanEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\OrderCreatedForSalesman  $event
     * @return void
     */
    public function handle(OrderCreatedForSalesman $event)
    {
        $order = $event->order;

        if (!$order->salesman || !$order->salesman->email) {
            return;
        }

        $data = [
            'order' => $order,
        ];

        Mail::to($order->salesman->email)->queue(new MailQueue(
            'backend.emails.order_created_salesman',
            $data,
            'New Order Created - Lamart Manufacturing'
        ));
    }
}
