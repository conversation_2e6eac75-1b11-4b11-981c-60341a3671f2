<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('item_price_level_lists', function (Blueprint $table) {
            $table->unsignedBigInteger('color_id')->nullable()->after('product_id');
            $table->decimal('custom_price', 10, 2)->after('price_level_list_id');
            $table->foreign('color_id')->references('id')->on('colors')->onDelete('cascade');
            $table->unique(['price_level_list_id', 'product_id', 'color_id'], 'price_product_color_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('item_price_level_lists', function (Blueprint $table) {
            $table->dropUnique('price_product_color_unique');
            $table->dropForeign(['color_id']);
            $table->dropColumn(['color_id', 'custom_price']);
        });
    }
};
