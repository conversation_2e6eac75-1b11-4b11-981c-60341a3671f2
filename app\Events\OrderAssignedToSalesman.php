<?php

namespace App\Events;

use App\Models\Order;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderAssignedToSalesman
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $salesman;
    public $previousSalesman;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param User $salesman
     * @param User|null $previousSalesman
     * @return void
     */
    public function __construct(Order $order, User $salesman, User $previousSalesman = null)
    {
        $this->order = $order;
        $this->salesman = $salesman;
        $this->previousSalesman = $previousSalesman;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
