<?php

namespace App\Listeners;

use App\Events\OrderStatusChanged;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderStatusChangedEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderStatusChanged  $event
     * @return void
     */
    public function handle(OrderStatusChanged $event)
    {
        $order = $event->order;
        $oldStatus = $event->oldStatus;
        $newStatus = $event->newStatus;

        // Send email to customer about status change
        $customerData = [
            'locale' => app()->getLocale(),
            'subject' => 'Order Status Update - #' . $order->order_number,
            'order' => $order,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'view_file' => 'backend.emails.order_status_changed_customer',
            'priority' => 'normal'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($customerData));

        // Send notification to salesman if assigned
        if ($order->salesman_id) {
            $salesmanData = [
                'locale' => app()->getLocale(),
                'subject' => 'Order Status Changed - #' . $order->order_number,
                'order' => $order,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'view_file' => 'backend.emails.order_status_changed_salesman',
                'priority' => 'normal'
            ];

            $salesman = $order->salesman;
            if ($salesman) {
                Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                    ->queue(new MailQueue($salesmanData));
            }
        }

        // Special handling for specific status changes
        switch ($newStatus) {
            case 'shipped':
                $this->handleShippedStatus($order);
                break;
            case 'delivered':
                $this->handleDeliveredStatus($order);
                break;
            case 'cancelled':
                $this->handleCancelledStatus($order);
                break;
        }
    }

    /**
     * Handle shipped status
     */
    private function handleShippedStatus($order)
    {
        $data = [
            'locale' => app()->getLocale(),
            'subject' => 'Your Order Has Shipped - #' . $order->order_number,
            'order' => $order,
            'view_file' => 'backend.emails.order_shipped_customer',
            'priority' => 'high'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($data, 'high'));
    }

    /**
     * Handle delivered status
     */
    private function handleDeliveredStatus($order)
    {
        $data = [
            'locale' => app()->getLocale(),
            'subject' => 'Order Delivered - Please Review - #' . $order->order_number,
            'order' => $order,
            'view_file' => 'backend.emails.order_delivered_customer',
            'priority' => 'normal'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($data));
    }

    /**
     * Handle cancelled status
     */
    private function handleCancelledStatus($order)
    {
        $data = [
            'locale' => app()->getLocale(),
            'subject' => 'Order Cancelled - #' . $order->order_number,
            'order' => $order,
            'view_file' => 'backend.emails.order_cancelled_customer',
            'priority' => 'high'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($data, 'high'));
    }
}
