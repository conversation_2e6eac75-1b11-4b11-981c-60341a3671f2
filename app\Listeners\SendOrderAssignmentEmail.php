<?php

namespace App\Listeners;

use App\Events\OrderAssignedToSalesman;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderAssignmentEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderAssignedToSalesman  $event
     * @return void
     */
    public function handle(OrderAssignedToSalesman $event)
    {
        $order = $event->order;
        $salesman = $event->salesman;
        $previousSalesman = $event->previousSalesman;

        // Send email to new salesman
        $salesmanData = [
            'order' => $order,
            'salesman' => $salesman,
        ];

        Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
            ->queue(new MailQueue(
                'backend.emails.order_assigned_salesman',
                $salesmanData,
                'New Order Assignment - #' . $order->order_number
            ));

        // Send email to customer about salesman assignment
        $customerData = [
            'order' => $order,
            'salesman' => $salesman,
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue(
                'backend.emails.salesman_assigned_customer',
                $customerData,
                'Your Sales Representative - Order #' . $order->order_number
            ));

        // If there was a previous salesman, notify them
        if ($previousSalesman) {
            $previousSalesmanData = [
                'order' => $order,
                'new_salesman' => $salesman,
            ];

            // Note: This template would need to be created if reassignment notifications are needed
            // Mail::to($previousSalesman->email, $previousSalesman->first_name . ' ' . $previousSalesman->last_name)
            //     ->queue(new MailQueue(
            //         'backend.emails.order_reassigned_previous_salesman',
            //         $previousSalesmanData,
            //         'Order Reassignment - #' . $order->order_number
            //     ));
        }
    }
}
