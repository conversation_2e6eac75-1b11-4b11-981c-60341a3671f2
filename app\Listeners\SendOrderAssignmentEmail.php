<?php

namespace App\Listeners;

use App\Events\OrderAssignedToSalesman;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderAssignmentEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderAssignedToSalesman  $event
     * @return void
     */
    public function handle(OrderAssignedToSalesman $event)
    {
        $order = $event->order;
        $salesman = $event->salesman;
        $previousSalesman = $event->previousSalesman;

        // Send email to new salesman
        $salesmanData = [
            'locale' => app()->getLocale(),
            'subject' => 'New Order Assignment - #' . $order->order_number,
            'order' => $order,
            'salesman' => $salesman,
            'view_file' => 'backend.emails.order_assigned_salesman',
            'priority' => 'high'
        ];

        Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
            ->queue(new MailQueue($salesmanData, 'high'));

        // Send email to customer about salesman assignment
        $customerData = [
            'locale' => app()->getLocale(),
            'subject' => 'Your Sales Representative - Order #' . $order->order_number,
            'order' => $order,
            'salesman' => $salesman,
            'view_file' => 'backend.emails.salesman_assigned_customer',
            'priority' => 'normal'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($customerData));

        // If there was a previous salesman, notify them
        if ($previousSalesman) {
            $previousSalesmanData = [
                'locale' => app()->getLocale(),
                'subject' => 'Order Reassignment - #' . $order->order_number,
                'order' => $order,
                'new_salesman' => $salesman,
                'view_file' => 'backend.emails.order_reassigned_previous_salesman',
                'priority' => 'normal'
            ];

            Mail::to($previousSalesman->email, $previousSalesman->first_name . ' ' . $previousSalesman->last_name)
                ->queue(new MailQueue($previousSalesmanData));
        }
    }
}
