<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\VisitReminder;
use App\Http\Controllers\EmailController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SendVisitReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-visit-reminders {--dry-run : Show what would be sent without actually sending}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send visit reminders to salesmen for today\'s scheduled visits';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $today = Carbon::today();
        
        $this->info("Processing visit reminders for: " . $today->format('Y-m-d'));

        // Get recurring reminders that need new instances created
        $recurringReminders = VisitReminder::where('is_recurring', true)
            ->whereDate('visit_date', $today)
            ->with(['customer', 'salesman'])
            ->get();

        // Get all reminders for today
        $reminders = VisitReminder::whereDate('visit_date', $today)
            ->with(['customer', 'salesman'])
            ->get();

        $this->info("Found " . $recurringReminders->count() . " recurring reminders to process");
        $this->info("Found " . $reminders->count() . " total reminders for today");

        $emailController = new EmailController();
        $sentCount = 0;
        $errorCount = 0;
        $createdCount = 0;

        // Process recurring reminders - create next instances
        foreach ($recurringReminders as $reminder) {
            try {
                $interval = $reminder->interval ?? 1;
                $frequency = $reminder->frequency ?? 'month';

                $nextVisitDate = match ($frequency) {
                    'day' => Carbon::parse($reminder->visit_date)->addDays($interval),
                    'week' => Carbon::parse($reminder->visit_date)->addWeeks($interval),
                    'month' => Carbon::parse($reminder->visit_date)->addMonths($interval),
                    'year' => Carbon::parse($reminder->visit_date)->addYears($interval),
                    default => Carbon::parse($reminder->visit_date)->addMonth(),
                };

                if (!$dryRun) {
                    VisitReminder::create([
                        'salesman_id' => $reminder->salesman_id,
                        'customer_id' => $reminder->customer_id,
                        'visit_date' => $nextVisitDate,
                        'note' => $reminder->note,
                        'is_recurring' => true,
                        'interval' => $interval,
                        'frequency' => $frequency,
                    ]);
                }

                $createdCount++;
                $this->line("Created next recurring reminder for {$reminder->customer->first_name} {$reminder->customer->last_name} on {$nextVisitDate->format('Y-m-d')}");
            } catch (\Exception $e) {
                $this->error("Failed to create recurring reminder: " . $e->getMessage());
                Log::error('Failed to create recurring visit reminder', [
                    'reminder_id' => $reminder->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Send reminder emails
        foreach ($reminders as $reminder) {
            if (!$reminder->customer || !$reminder->customer->email || !$reminder->salesman) {
                $this->warn("Skipping reminder ID {$reminder->id} - missing customer email or salesman");
                continue;
            }

            try {
                if (!$dryRun) {
                    $emailController->send_visit_reminders_salesman($reminder);
                }
                
                $sentCount++;
                $this->line("Sent reminder to {$reminder->salesman->first_name} {$reminder->salesman->last_name} for customer {$reminder->customer->first_name} {$reminder->customer->last_name}");
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("Failed to send reminder to {$reminder->salesman->email}: " . $e->getMessage());
                Log::error('Failed to send visit reminder email', [
                    'reminder_id' => $reminder->id,
                    'salesman_email' => $reminder->salesman->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("\n=== Summary ===");
        $this->info("Recurring reminders created: {$createdCount}");
        $this->info("Reminder emails sent: {$sentCount}");
        if ($errorCount > 0) {
            $this->error("Errors encountered: {$errorCount}");
        }

        if ($dryRun) {
            $this->warn("DRY RUN - No emails were actually sent or reminders created");
        }

        Log::info('Visit reminders processed', [
            'date' => $today->format('Y-m-d'),
            'sent_count' => $sentCount,
            'error_count' => $errorCount,
            'created_count' => $createdCount,
            'dry_run' => $dryRun
        ]);

        return $errorCount > 0 ? 1 : 0;
    }
}
