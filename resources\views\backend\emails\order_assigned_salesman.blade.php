@extends('emails.layouts.master')

@section('title', 'New Order Assignment - Lamart Manufacturing')

@section('preheader', 'You have been assigned order #' . $order['order_number'])

@section('content')
<!-- Assignment Message -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 2em 0 0 0;">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text">
                  <h2 style="color: #4D734E;">New Order Assignment</h2>
                  <h4>
                     Hello {{$salesman->first_name}} {{$salesman->last_name}},<br><br>
                     You have been assigned a new order. Please review the details below and take appropriate action.
                  </h4>
                  <p>
                     Order Number: <strong>{{$order['order_number']}}</strong><br>
                     Order Date: {{date('M d, Y', strtotime($order['created_at']))}} <br>
                     Customer: {{$order['first_name']}} {{$order['last_name']}}<br>
                     Total Amount: <strong style="color: #4D734E;">${{number_format($order['total_amount'], 2)}}</strong>
                  </p>
                  <p style="margin-top: 20px;">
                     <a href="https://lamartmfg.com/admin/order/{{$order['id']}}" class="btn btn-primary" style="background: #4D734E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                        View Order Details
                     </a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Customer Information -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Customer Information</h3>
                  <p style="margin: 5px 0;"><strong>Name:</strong> {{$order['first_name']}} {{$order['last_name']}}</p>
                  <p style="margin: 5px 0;"><strong>Email:</strong> {{$order['email']}}</p>
                  <p style="margin: 5px 0;"><strong>Phone:</strong> {{$order['phone']}}</p>
                  <p style="margin: 5px 0;"><strong>Address:</strong> {{$order['address1']}}{{$order['address2'] ? ', ' . $order['address2'] : ''}}, {{$order['post_code']}}, {{$order['country']}}</p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Order Summary -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Order Summary</h3>
                  
                  @if(isset($order['cart_info']) && is_array($order['cart_info']))
                     @foreach($order['cart_info'] as $item)
                     <div style="padding: 10px 0; border-bottom: 1px solid #eee;">
                        <p style="margin: 0; font-weight: 500;">{{$item['title']}}</p>
                        <p style="margin: 0; color: #666; font-size: 14px;">
                           Quantity: {{$item['quantity']}} × ${{number_format($item['price'], 2)}} = ${{number_format($item['price'] * $item['quantity'], 2)}}
                        </p>
                     </div>
                     @endforeach
                  @endif

                  <div style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 4px;">
                     <p style="margin: 0; font-size: 18px; font-weight: 600; color: #4D734E;">
                        Total: ${{number_format($order['total_amount'], 2)}}
                     </p>
                  </div>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Next Steps -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Next Steps</h3>
                  <ul style="margin: 0; padding-left: 20px;">
                     <li>Review the order details and customer information</li>
                     <li>Contact the customer if needed for clarification</li>
                     <li>Coordinate with the warehouse for order fulfillment</li>
                     <li>Update the order status as it progresses</li>
                  </ul>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>
@endsection
