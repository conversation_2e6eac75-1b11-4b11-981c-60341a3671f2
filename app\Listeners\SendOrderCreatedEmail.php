<?php

namespace App\Listeners;

use App\Events\OrderCreated;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Models\User;

class SendOrderCreatedEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderCreated  $event
     * @return void
     */
    public function handle(OrderCreated $event)
    {
        $order = $event->order;

        // Send email to customer
        $customerData = [
            'order' => $order,
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue(
                'backend.emails.order_details_customer',
                $customerData,
                'Order Confirmation - #' . $order->order_number
            ));

        // Send notification to assigned salesman if exists
        if ($order->salesman_id) {
            $salesman = User::find($order->salesman_id);
            if ($salesman) {
                Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                    ->queue(new MailQueue(
                        'backend.emails.order_assigned_salesman',
                        $customerData,
                        'New Order Assignment - #' . $order->order_number
                    ));
            }
        }

        // Send notification to picker only if there's a picker assigned to the order
        if ($order->picker_id) {
            $picker = User::find($order->picker_id);
            if ($picker) {
                Mail::to($picker->email, $picker->first_name . ' ' . $picker->last_name)
                    ->queue(new MailQueue(
                        'backend.emails.order_picker_notification',
                        $customerData,
                        'New Order for Picking - #' . $order->order_number
                    ));
            }
        } else {
            // If no specific picker assigned, send to all pickers
            $pickers = User::where('role', 'picker')->get();
            foreach ($pickers as $picker) {
                Mail::to($picker->email, $picker->first_name . ' ' . $picker->last_name)
                    ->queue(new MailQueue(
                        'backend.emails.order_picker_notification',
                        $customerData,
                        'New Order for Picking - #' . $order->order_number
                    ));
            }
        }
    }
}
