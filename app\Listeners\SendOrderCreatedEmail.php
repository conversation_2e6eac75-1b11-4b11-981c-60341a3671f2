<?php

namespace App\Listeners;

use App\Events\OrderCreated;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Models\User;

class SendOrderCreatedEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderCreated  $event
     * @return void
     */
    public function handle(OrderCreated $event)
    {
        $order = $event->order;

        // Send email to customer
        $customerData = [
            'locale' => app()->getLocale(),
            'subject' => 'Order Confirmation - #' . $order->order_number,
            'order' => $order,
            'view_file' => 'backend.emails.order_details_customer',
            'priority' => 'high'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue($customerData, 'high'));

        // Send notification to admin
        $adminData = [
            'locale' => app()->getLocale(),
            'subject' => 'New Order Received - #' . $order->order_number,
            'order' => $order,
            'view_file' => 'backend.emails.order_notification_admin',
            'priority' => 'high'
        ];

        $admins = User::where('role', 'admin')->get();
        foreach ($admins as $admin) {
            Mail::to($admin->email, $admin->first_name . ' ' . $admin->last_name)
                ->queue(new MailQueue($adminData, 'high'));
        }

        // Send notification to assigned salesman if exists
        if ($order->salesman_id) {
            $salesmanData = [
                'locale' => app()->getLocale(),
                'subject' => 'New Order Assignment - #' . $order->order_number,
                'order' => $order,
                'view_file' => 'backend.emails.order_assigned_salesman',
                'priority' => 'high'
            ];

            $salesman = User::find($order->salesman_id);
            if ($salesman) {
                Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                    ->queue(new MailQueue($salesmanData, 'high'));
            }
        }

        // // Send notification to picker
        // $pickerData = [
        //     'locale' => app()->getLocale(),
        //     'subject' => 'New Order for Picking - #' . $order->order_number,
        //     'order' => $order,
        //     'view_file' => 'backend.emails.order_picker_notification',
        //     'priority' => 'high'
        // ];

        // $pickers = User::where('role', 'picker')->get();
        // foreach ($pickers as $picker) {
        //     Mail::to($picker->email, $picker->first_name . ' ' . $picker->last_name)
        //         ->queue(new MailQueue($pickerData, 'high'));
        // }
    }
}
