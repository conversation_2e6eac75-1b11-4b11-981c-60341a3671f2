<?php

namespace App\Listeners;

use App\Events\UserAssignedToSalesman;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendUserAssignmentEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UserAssignedToSalesman  $event
     * @return void
     */
    public function handle(UserAssignedToSalesman $event)
    {
        $customer = $event->customer;
        $salesman = $event->salesman;
        $previousSalesman = $event->previousSalesman;

        // Send email to customer about new salesman assignment
        $customerData = [
            'locale' => app()->getLocale(),
            'subject' => 'Your New Sales Representative',
            'customer' => $customer,
            'salesman' => $salesman,
            'view_file' => 'backend.emails.user_assigned_salesman_customer',
            'priority' => 'normal'
        ];

        Mail::to($customer->email, $customer->first_name . ' ' . $customer->last_name)
            ->queue(new MailQueue($customerData));

        // Send email to new salesman about customer assignment
        $salesmanData = [
            'locale' => app()->getLocale(),
            'subject' => 'New Customer Assignment - ' . $customer->first_name . ' ' . $customer->last_name,
            'customer' => $customer,
            'salesman' => $salesman,
            'view_file' => 'backend.emails.user_assigned_salesman_notification',
            'priority' => 'normal'
        ];

        Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
            ->queue(new MailQueue($salesmanData));

        // If there was a previous salesman, notify them
        if ($previousSalesman) {
            $previousSalesmanData = [
                'locale' => app()->getLocale(),
                'subject' => 'Customer Reassignment - ' . $customer->first_name . ' ' . $customer->last_name,
                'customer' => $customer,
                'new_salesman' => $salesman,
                'view_file' => 'backend.emails.user_reassigned_previous_salesman',
                'priority' => 'normal'
            ];

            Mail::to($previousSalesman->email, $previousSalesman->first_name . ' ' . $previousSalesman->last_name)
                ->queue(new MailQueue($previousSalesmanData));
        }
    }
}
