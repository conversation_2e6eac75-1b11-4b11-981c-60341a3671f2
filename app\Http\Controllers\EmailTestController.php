<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Mail;
use App\Models\Order;
use App\Models\User;
use App\Models\VisitReminder;
use App\Models\Product;
use App\Mail\MailQueue;
use Carbon\Carbon;

class EmailTestController extends Controller
{
    /**
     * Show email testing dashboard
     */
    public function index()
    {
        $templates = $this->getAvailableTemplates();
        return view('backend.email-testing.index', compact('templates'));
    }

    /**
     * Preview email template
     */
    public function preview(Request $request, $template)
    {
        try {
            $data = $this->getTestData($template);
            
            if (!View::exists($template)) {
                abort(404, "Template '{$template}' not found");
            }

            return view($template, $data);
        } catch (\Exception $e) {
            return response()->view('backend.email-testing.error', [
                'error' => $e->getMessage(),
                'template' => $template
            ], 500);
        }
    }

    /**
     * Send test email
     */
    public function sendTest(Request $request)
    {
        $request->validate([
            'template' => 'required|string',
            'email' => 'required|email',
            'custom_data' => 'nullable|json'
        ]);

        try {
            $template = $request->template;
            $email = $request->email;
            $customData = $request->custom_data ? json_decode($request->custom_data, true) : [];

            $data = array_merge($this->getTestData($template), $customData, [
                'subject' => 'Test Email - ' . ucfirst(str_replace(['_', '-', '.'], ' ', $template)),
                'view_file' => $template,
                'priority' => 'high'
            ]);

            Mail::to($email)->queue(new MailQueue($data, 'high'));

            return response()->json([
                'success' => true,
                'message' => "Test email sent to {$email}"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available email templates
     */
    private function getAvailableTemplates()
    {
        return [
            'Order Emails' => [
                'backend.emails.order_details_customer' => 'Order Confirmation (Customer)',
                'backend.emails.order_assigned_salesman' => 'Order Assigned (Salesman)',
                'backend.emails.salesman_assigned_customer' => 'Salesman Assigned (Customer)',
                'backend.emails.order_status_changed_customer' => 'Order Status Changed (Customer)',
                'backend.emails.order_picker_notification' => 'Picker Notification',
            ],
            'User Management' => [
                'emails.registered' => 'User Registration',
                'emails.created' => 'Admin Created Account',
                'emails.user_status' => 'User Status Changed',
                'backend.emails.user_assigned_salesman_customer' => 'User Assigned to Salesman',
            ],
            'Visit & Return Emails' => [
                'backend.emails.salesman-visit-reminder' => 'Visit Reminder',
                'backend.emails.salesman-return-request' => 'Return Request',
            ],
            'Promotional' => [
                'backend.emails.promotional_email' => 'Promotional Email',
            ]
        ];
    }

    /**
     * Get test data for specific template
     */
    private function getTestData($template)
    {
        $baseData = [
            'locale' => app()->getLocale(),
        ];

        switch ($template) {
            case 'backend.emails.order_details_customer':
            case 'backend.emails.order_assigned_salesman':
            case 'backend.emails.salesman_assigned_customer':
            case 'backend.emails.order_status_changed_customer':
            case 'backend.emails.order_picker_notification':
                return array_merge($baseData, $this->getOrderTestData());

            case 'emails.registered':
            case 'emails.created':
            case 'emails.user_status':
                return array_merge($baseData, $this->getUserTestData());

            case 'backend.emails.user_assigned_salesman_customer':
                return array_merge($baseData, $this->getUserAssignmentTestData());

            case 'backend.emails.salesman-visit-reminder':
                return array_merge($baseData, $this->getVisitReminderTestData());

            case 'backend.emails.salesman-return-request':
                return array_merge($baseData, $this->getReturnRequestTestData());

            case 'backend.emails.promotional_email':
                return array_merge($baseData, $this->getPromotionalTestData());

            default:
                return $baseData;
        }
    }

    /**
     * Get test order data
     */
    private function getOrderTestData()
    {
        $order = Order::with(['user'])->latest()->first();
        
        if (!$order) {
            // Create mock order data
            $order = [
                'id' => 1,
                'order_number' => 'ORD-' . date('Y') . '-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'address1' => '123 Main Street',
                'address2' => 'Apt 4B',
                'post_code' => '10001',
                'country' => 'United States',
                'sub_total' => 150.00,
                'coupon' => 15.00,
                'delivery_charge' => 10.00,
                'total_amount' => 145.00,
                'status' => 'processing',
                'created_at' => now(),
                'cart_info' => [
                    [
                        'title' => 'Premium Widget',
                        'quantity' => 2,
                        'price' => 50.00,
                        'size' => 'Large',
                        'color' => 'Blue',
                        'photo' => 'products/sample-product.jpg',
                        'sku' => 'PWD-001'
                    ],
                    [
                        'title' => 'Standard Component',
                        'quantity' => 1,
                        'price' => 50.00,
                        'size' => 'Medium',
                        'color' => 'Red',
                        'photo' => 'products/sample-product-2.jpg',
                        'sku' => 'SC-002'
                    ]
                ]
            ];
        }

        $salesman = User::where('role', 'salesman')->first() ?? $this->getMockUser('salesman');

        return [
            'order' => $order,
            'salesman' => $salesman,
            'old_status' => 'pending',
            'new_status' => 'processing'
        ];
    }

    /**
     * Get test user data
     */
    private function getUserTestData()
    {
        return [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'name' => 'Jane Smith',
            'password' => 'TempPassword123',
            'status' => 'active'
        ];
    }

    /**
     * Get user assignment test data
     */
    private function getUserAssignmentTestData()
    {
        $customer = User::where('role', 'customer')->first() ?? $this->getMockUser('customer');
        $salesman = User::where('role', 'salesman')->first() ?? $this->getMockUser('salesman');

        return [
            'customer' => $customer,
            'salesman' => $salesman
        ];
    }

    /**
     * Get visit reminder test data
     */
    private function getVisitReminderTestData()
    {
        $reminder = VisitReminder::with(['customer', 'salesman'])->first();
        
        if (!$reminder) {
            $reminder = (object) [
                'id' => 1,
                'visit_date' => Carbon::today()->addDays(1),
                'note' => 'Follow up on recent order and discuss new product line',
                'customer' => $this->getMockUser('customer'),
                'salesman' => $this->getMockUser('salesman')
            ];
        }

        return ['reminder' => $reminder];
    }

    /**
     * Get return request test data
     */
    private function getReturnRequestTestData()
    {
        $orderData = $this->getOrderTestData();
        
        return array_merge($orderData, [
            'returns' => [
                [
                    'product_id' => 1,
                    'quantity' => 1,
                    'reason' => 'Defective item'
                ]
            ],
            'reason' => 'Product arrived damaged and does not meet quality standards'
        ]);
    }

    /**
     * Get promotional email test data
     */
    private function getPromotionalTestData()
    {
        return [
            'subject' => 'Special 25% Off Sale - Limited Time!',
            'preheader' => 'Don\'t miss out on our biggest sale of the year!',
            'headline' => '25% Off Everything!',
            'subheadline' => 'Limited time offer - ends this weekend',
            'discount_percentage' => 25,
            'discount_description' => 'On all products',
            'offer_title' => 'Weekend Flash Sale',
            'offer_description' => '<p>Take advantage of our biggest sale of the year! Get 25% off on all products including our premium widget collection.</p>',
            'offer_features' => [
                'Free shipping on orders over $100',
                '30-day money-back guarantee',
                'Expert customer support',
                'Premium quality products'
            ],
            'expiry_date' => Carbon::now()->addDays(3)->format('Y-m-d'),
            'promo_code' => 'SAVE25NOW',
            'featured_products' => [
                [
                    'name' => 'Premium Widget Pro',
                    'description' => 'Our flagship product with advanced features',
                    'price' => 75.00,
                    'original_price' => 100.00,
                    'image' => asset('images/sample-product.jpg'),
                    'url' => 'https://lamartmfg.com/product/premium-widget-pro'
                ],
                [
                    'name' => 'Standard Component Set',
                    'description' => 'Essential components for everyday use',
                    'price' => 37.50,
                    'original_price' => 50.00,
                    'image' => asset('images/sample-product-2.jpg'),
                    'url' => 'https://lamartmfg.com/product/standard-component-set'
                ]
            ],
            'cta_title' => 'Ready to Save Big?',
            'cta_description' => 'Don\'t wait - this amazing offer ends soon!',
            'cta_text' => 'Shop Now & Save 25%',
            'cta_url' => 'https://lamartmfg.com/sale',
            'secondary_cta_text' => 'Browse All Products',
            'secondary_cta_url' => 'https://lamartmfg.com/products',
            'unsubscribe_url' => 'https://lamartmfg.com/unsubscribe',
            'preferences_url' => 'https://lamartmfg.com/email-preferences'
        ];
    }

    /**
     * Get mock user data
     */
    private function getMockUser($role)
    {
        $users = [
            'customer' => [
                'id' => 1,
                'first_name' => 'John',
                'last_name' => 'Customer',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'role' => 'customer',
                'address' => '123 Customer Street, City, State 12345'
            ],
            'salesman' => [
                'id' => 2,
                'first_name' => 'Mike',
                'last_name' => 'Salesman',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'role' => 'salesman',
                'address' => '456 Sales Avenue, City, State 67890'
            ]
        ];

        return (object) $users[$role];
    }
}
