<?php

    use Illuminate\Support\Facades\Route;
    use Illuminate\Support\Facades\Artisan;
    use App\Http\Controllers\AdminController;
    usE App\Http\Controllers\SalesmanController;
    use App\Http\Controllers\Auth\ForgotPasswordController;
    use App\Http\Controllers\FrontendController;
    use App\Http\Controllers\Auth\LoginController;
    use App\Http\Controllers\MessageController;
    use App\Http\Controllers\QuickBooksController;
    use App\Http\Controllers\CartController;
    use App\Http\Controllers\WishlistController;
    use App\Http\Controllers\OrderController;
    use App\Http\Controllers\Admin\OrderController as AdminOrderController;
    use App\Http\Controllers\Admin\ReturnController as AdminReturnController;
    use App\Http\Controllers\Admin\VisitReminderController as AdminVisitReminderController;
    use App\Http\Controllers\Salesman\VisitReminderController as SalesmanVisitReminderController;
    use App\Http\Controllers\Salesman\OrderController as SalesmanOrderController;
    use App\Http\Controllers\ProductReviewController;
    use App\Http\Controllers\UsersController;
    use App\Http\Controllers\PostCommentController;
    use App\Http\Controllers\CronController;
    use App\Http\Controllers\CouponController;
    use App\Http\Controllers\PayPalController;
    use App\Http\Controllers\NotificationController;
    use App\Http\Controllers\HomeController;
    use \UniSharp\LaravelFilemanager\Lfm;
    use App\Http\Controllers\Auth\ResetPasswordController;
    use App\Http\Controllers\Admin\ImportCustomerController;
    use App\Http\Controllers\Admin\ReportController;
    use App\Http\Controllers\Salesman\ReportController as SalesmanReportController;
    use App\Http\Controllers\PriceLevelListController;

    /*
    |--------------------------------------------------------------------------
    | Web Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register web routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | contains the "web" middleware group. Now create something great!
    |
    */

    // CACHE CLEAR ROUTE
    Route::get('cache-clear', function () {
        Artisan::call('optimize:clear');
        request()->session()->flash('success', 'Successfully cache cleared.');
        return redirect()->back();
    })->name('cache.clear');


    // STORAGE LINKED ROUTE
    Route::get('storage-link',[AdminController::class,'storageLink'])->name('storage.link');


    Auth::routes(['register' => false]);

    Route::get('user/login', [FrontendController::class, 'login'])->name('login.form');
    Route::post('user/login', [FrontendController::class, 'loginSubmit'])->name('login.submit');
    Route::get('user/logout', [FrontendController::class, 'logout'])->name('user.logout');

    Route::get('user/register', [FrontendController::class, 'register'])->name('register.form');
    Route::post('user/w9', [FrontendController::class, 'w9Submit'])->name('w9');
    Route::get('user/w9', [FrontendController::class, 'w9'])->name('w9.form');
    Route::get('user/check-w9', [FrontendController::class, 'w9Check'])->name('check.w9');
    Route::post('user/register', [FrontendController::class, 'registerSubmit'])->name('register.submit');

    // Reset password
    Route::get('password/reset', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');
    // Password Reset Routes
    Route::get('password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');

    // Socialite
    Route::get('login/{provider}/', [LoginController::class, 'redirect'])->name('login.redirect');
    Route::get('login/{provider}/callback/', [LoginController::class, 'Callback'])->name('login.callback');

    Route::get('/', [FrontendController::class, 'home'])->name('home');

// Frontend Routes
    Route::get('/cron/send_visit_reminders_salesman', [CronController::class, 'send_visit_reminders_salesman']);
    Route::get('/home', [FrontendController::class, 'index']);
    Route::get('/about-us', [FrontendController::class, 'aboutUs'])->name('about-us');
    Route::get('/contact', [FrontendController::class, 'contact'])->name('contact');
    Route::post('/contact/message', [MessageController::class, 'store'])->name('contact.store');
    Route::get('product-detail/{slug}', [FrontendController::class, 'productDetail'])->name('product-detail');
    Route::post('/product/search', [FrontendController::class, 'productSearch'])->name('product.search');
    Route::get('/product-cat/{slug}', [FrontendController::class, 'productCat'])->name('product-cat');
    Route::get('/product-sub-cat/{slug}/{sub_slug}', [FrontendController::class, 'productSubCat'])->name('product-sub-cat');
    Route::get('/product-brand/{slug}', [FrontendController::class, 'productBrand'])->name('product-brand');
// Cart section
    Route::get('/add-to-cart/{slug}', [CartController::class, 'addToCart'])->name('add-to-cart')->middleware('user');
    Route::post('/add-to-cart', [CartController::class, 'singleAddToCart'])->name('single-add-to-cart')->middleware('user');
    Route::get('cart-delete/{id}', [CartController::class, 'cartDelete'])->name('cart-delete');
    Route::post('cart-update', [CartController::class, 'cartUpdate'])->name('cart.update');

    Route::get('/cart', function () {
        return view('frontend.pages.cart');
    })->name('cart');
    Route::get('/checkout', [CartController::class, 'checkout'])->name('checkout')->middleware('user');
// Wishlist
    Route::get('/wishlist', function () {
        return view('frontend.pages.wishlist');
    })->name('wishlist');
    Route::get('/wishlist/{slug}', [WishlistController::class, 'wishlist'])->name('add-to-wishlist')->middleware('user');
    Route::get('wishlist-delete/{id}', [WishlistController::class, 'wishlistDelete'])->name('wishlist-delete');
    Route::post('cart/order', [OrderController::class, 'store'])->name('cart.order');
    Route::get('order/pdf/{id}', [OrderController::class, 'pdf'])->name('order.pdf');
    Route::get('/income', [OrderController::class, 'incomeChart'])->name('product.order.income');
// Route::get('/user/chart',[AdminController::class, 'userPieChart'])->name('user.piechart');
    Route::get('/product-grids', [FrontendController::class, 'productGrids'])->name('product-grids');
    Route::get('/product-lists', [FrontendController::class, 'productLists'])->name('product-lists');
    Route::match(['get', 'post'], '/filter', [FrontendController::class, 'productFilter'])->name('shop.filter');
// Order Track
    Route::get('/product/track', [OrderController::class, 'orderTrack'])->name('order.track');
    Route::post('product/track/order', [OrderController::class, 'productTrackOrder'])->name('product.track.order');
// Blog
    Route::get('/blog', [FrontendController::class, 'blog'])->name('blog');
    Route::get('/blog-detail/{slug}', [FrontendController::class, 'blogDetail'])->name('blog.detail');
    Route::get('/blog/search', [FrontendController::class, 'blogSearch'])->name('blog.search');
    Route::post('/blog/filter', [FrontendController::class, 'blogFilter'])->name('blog.filter');
    Route::get('blog-cat/{slug}', [FrontendController::class, 'blogByCategory'])->name('blog.category');
    Route::get('blog-tag/{slug}', [FrontendController::class, 'blogByTag'])->name('blog.tag');

// NewsLetter
    Route::post('/subscribe', [FrontendController::class, 'subscribe'])->name('subscribe');

// Product Review
    Route::resource('/review', 'ProductReviewController');
    Route::post('product/{slug}/review', [ProductReviewController::class, 'store'])->name('product.review.store');
    Route::delete('review/{id}/delete-image/{image}', [ProductReviewController::class, 'deleteImage'])->name('review.delete.image');

// Post Comment
    Route::post('post/{slug}/comment', [PostCommentController::class, 'store'])->name('post-comment.store');
    Route::resource('/comment', 'PostCommentController');
// Coupon
    Route::post('/coupon-store', [CouponController::class, 'couponStore'])->name('coupon-store');
// Payment
    Route::get('payment', [PayPalController::class, 'payment'])->name('payment');
    Route::get('cancel', [PayPalController::class, 'cancel'])->name('payment.cancel');
    Route::get('payment/success', [PayPalController::class, 'success'])->name('payment.success');


// Backend section start

    Route::group(['prefix' => '/admin', 'middleware' => ['auth', 'admin']], function () {
        Route::get('/', [AdminController::class, 'index'])->name('admin');
        Route::get('/file-manager', function () {
            return view('backend.layouts.file-manager');
        })->name('file-manager');
        // user route
        Route::resource('users', 'UsersController');
        // Banner
        Route::resource('banner', 'BannerController');
        // Brand
        Route::resource('brand', 'BrandController');
        // Profile
        Route::get('/profile', [AdminController::class, 'profile'])->name('admin-profile');
        Route::post('/profile/{id}', [AdminController::class, 'profileUpdate'])->name('profile-update');
        // Category
        Route::resource('/category', 'CategoryController');

        Route::resource('/list', 'PriceLevelListController');
        // Product
        Route::resource('/product', 'ProductController');

        Route::resource('/size', 'SizeController');

        Route::resource('/color', 'ColorController');
        // Ajax for sub category
        Route::post('/category/{id}/child', 'CategoryController@getChildByParent');
        // POST category
        Route::resource('/post-category', 'PostCategoryController');
        // Post tag
        Route::resource('/post-tag', 'PostTagController');
        // Post
        Route::resource('/post', 'PostController');
        // Message
        Route::resource('/message', 'MessageController');
        Route::get('/message/five', [MessageController::class, 'messageFive'])->name('messages.five');

        // Order
        Route::resource('/order', 'OrderController');
        // Shipping
        Route::resource('/shipping', 'ShippingController');
        // Coupon
        Route::resource('/coupon', 'CouponController');
        // Settings
        Route::get('settings', [AdminController::class, 'settings'])->name('settings');
        Route::post('setting/update', [AdminController::class, 'settingsUpdate'])->name('settings.update');

        Route::get('/customers/map', [UsersController::class, 'map'])->name('map');

        Route::get('/qbo/auth', [QuickBooksController::class, 'authRedirect'])->name('qbo.auth');
        Route::get('/qbo/callback', [QuickBooksController::class, 'callback']);
        Route::get('/qbo/customers', [QuickBooksController::class, 'getCustomers']);
        // Notification
        Route::get('/notification/{id}', [NotificationController::class, 'show'])->name('admin.notification');
        Route::get('/notifications', [NotificationController::class, 'index'])->name('all.notification');
        Route::delete('/notification/{id}', [NotificationController::class, 'delete'])->name('notification.delete');
        // Password Change
        Route::get('change-password', [AdminController::class, 'changePassword'])->name('change.password.form');
        Route::post('change-password', [AdminController::class, 'changPasswordStore'])->name('change.password');


        Route::get('/orders/index', [AdminOrderController::class, 'index'])->name('orders.index');
        Route::get('/orders/show/{id}', [AdminOrderController::class, 'show'])->name('orders.show');
        Route::get('/orders/generate_pdf/{id}', [AdminOrderController::class, 'generate_pdf'])->name('orders.pdf');
        Route::get('/orders/create', [AdminOrderController::class, 'create'])->name('orders.create');
        Route::post('/orders', [AdminOrderController::class, 'store'])->name('orders.store');
        Route::post('/returns', [AdminReturnController::class, 'store'])->name('admin.returns.store');
        Route::get('/orders/edit/{id}', [AdminOrderController::class, 'edit'])->name('orders.edit');
        Route::put('/orders/update/{id}', [AdminOrderController::class, 'update'])->name('orders.update');
        Route::get('/orders/destroy/{id}', [AdminOrderController::class, 'destroy'])->name('orders.destroy');
        Route::get('products/prices', [AdminOrderController::class, 'getProductPrices'])->name('admin.products.prices');
        Route::post('orders/check-stock', [AdminOrderController::class, 'checkStock'])->name('orders.check-stock');
        Route::post('/orders/{id}/send', [AdminOrderController::class, 'sendOrderDetails'])->name('admin.orders.send');
        Route::get('/orders/{id}/send', [AdminOrderController::class, 'sendOrderDetails']);
        Route::get('/salesman/{id}/customers', function ($id) {
            $customers = \App\Models\User::whereIn('role', ['customer','user'])
                ->where('salesman_id', $id)->get(['id', 'first_name', 'last_name', 'email', 'shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip', 'contact_phone', 'company_name']);
            return response()->json($customers);
        })->name('admin.salesman.customers');
        Route::get('/customer/previous_orders/{id}', function ($id) {
            $prev_orders = \App\Models\Order::with('items', 'items.product')->where('user_id', $id)->latest()->get();
            return response()->json($prev_orders);
        })->name('admin.customer.orders');

        Route::get('visit-reminders/create','AdminVisitReminderController@create')->name('admin.visit.create');
        Route::post('visit-reminders','AdminVisitReminderController@store')->name('admin.visit.store');

        Route::get('/import', [ImportCustomerController::class, 'showForm'])->name('import.form');
        Route::post('/import-customers', [ImportCustomerController::class, 'importCustomers'])->name('import.customers');
        Route::post('/import-products', [ImportCustomerController::class, 'importProducts'])->name('import.products');

        Route::get('/report/orders', [ReportController::class, 'index'])->name('admin.report.index');
        Route::get('/report/customers/{salesman_id}', [ReportController::class, 'getCustomers'])->name('admin.report.customers');
        Route::post('/users/check-email', [UsersController::class, 'checkEmail'])->name('users.check-email');
        Route::post('/products/details', [PriceLevelListController::class, 'getProductDetails'])->name('admin.products.details');

    });


// User section start
    Route::group(['prefix' => '/user', 'middleware' => ['user']], function () {
        Route::get('/', [HomeController::class, 'index'])->name('user');
        // Profile
        Route::get('/profile', [HomeController::class, 'profile'])->name('user-profile');
        Route::post('/profile/{id}', [HomeController::class, 'profileUpdate'])->name('user-profile-update');
        //  Order
        Route::get('/order', "HomeController@orderIndex")->name('user.order.index');
        Route::get('/order/show/{id}', "HomeController@orderShow")->name('user.order.show');
        Route::delete('/order/delete/{id}', [HomeController::class, 'userOrderDelete'])->name('user.order.delete');
        // Product Review
        Route::get('/user-review', [HomeController::class, 'productReviewIndex'])->name('user.productreview.index');
        Route::delete('/user-review/delete/{id}', [HomeController::class, 'productReviewDelete'])->name('user.productreview.delete');
        Route::get('/user-review/edit/{id}', [HomeController::class, 'productReviewEdit'])->name('user.productreview.edit');
        Route::patch('/user-review/update/{id}', [HomeController::class, 'productReviewUpdate'])->name('user.productreview.update');

        // Post comment
        Route::get('user-post/comment', [HomeController::class, 'userComment'])->name('user.post-comment.index');
        Route::delete('user-post/comment/delete/{id}', [HomeController::class, 'userCommentDelete'])->name('user.post-comment.delete');
        Route::get('user-post/comment/edit/{id}', [HomeController::class, 'userCommentEdit'])->name('user.post-comment.edit');
        Route::patch('user-post/comment/udpate/{id}', [HomeController::class, 'userCommentUpdate'])->name('user.post-comment.update');

        // Password Change
        Route::get('change-password', [HomeController::class, 'changePassword'])->name('user.change.password.form');
        Route::post('change-password', [HomeController::class, 'changPasswordStore'])->name('change.password');

    });

    Route::prefix('/salesman')->middleware(['auth','salesman'])->group(function(){
        Route::name('salesman.')->group(function () {
        Route::resource('users', 'UsersController');
        });
        Route::get('/', [SalesmanController::class, 'index'])->name('salesman');
        Route::get('/customers/map', [UsersController::class, 'map'])->name('map');
        Route::get('/navigate/{user}', [UsersController::class, 'navigate'])->name('salesman.navigate');
        Route::get('/orders', [SalesmanOrderController::class, 'index'])->name('salesman.orders');
        Route::get('/orders/show/{id}', [SalesmanOrderController::class, 'show'])->name('salesman.orders.show');
        Route::get('/orders/create', [SalesmanOrderController::class, 'create'])->name('salesman.orders.create');
        Route::post('/orders', [SalesmanOrderController::class, 'store'])->name('salesman.orders.store');
        Route::get('/orders/edit/{id}', [SalesmanOrderController::class, 'edit'])->name('salesman.orders.edit');
        Route::put('/orders/update/{id}', [SalesmanOrderController::class, 'update'])->name('salesman.orders.update');
        Route::post('/orders/check-stock', [SalesmanOrderController::class, 'checkStock'])->name('salesman.orders.check-stock');
        Route::get('/products/prices', [SalesmanOrderController::class, 'getProductPrices'])->name('salesman.products.prices');
        Route::get('visit-reminders',[SalesmanVisitReminderController::class, 'index'])->name('salesman.visit.index');
        Route::get('visit-reminders/create',[SalesmanVisitReminderController::class, 'create'])->name('salesman.visit.create');
        Route::post('visit-reminders/store',[SalesmanVisitReminderController::class, 'store'])->name('salesman.visit.store');
        Route::get('visit-reminders/edit/{id}',[SalesmanVisitReminderController::class, 'edit'])->name('salesman.visit.edit');
        Route::put('visit-reminders/update/{id}',[SalesmanVisitReminderController::class, 'update'])->name('salesman.visit.update');
        Route::delete('visit-reminders/destroy/{id}',[SalesmanVisitReminderController::class, 'destroy'])->name('salesman.visit.destroy');
        Route::post('/orders/{id}/send', [SalesmanOrderController::class, 'sendOrderDetails'])->name('salesman.orders.send');
        Route::get('/orders/{id}/return', [SalesmanOrderController::class, 'order_return'])->name('salesman.return.create');
        Route::post('/orders/return', [AdminReturnController::class, 'store'])->name('salesman.return.store');
        Route::get('/report/orders', [SalesmanReportController::class, 'index'])->name('salesman.report.index');
        Route::get('/customer/previous_orders/{id}', function ($id) {
            $prev_orders = \App\Models\Order::with('items', 'items.product')->where('user_id', $id)->latest()->get();
            return response()->json($prev_orders);
        })->name('salesman.customer.orders');
        Route::get('/{id}/customers', function ($id) {
            $customers = \App\Models\User::whereIn('role', ['customer','user'])
                ->where('salesman_id', $id)->get(['id', 'first_name', 'last_name', 'email', 'shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip', 'contact_phone', 'company_name']);
            return response()->json($customers);
        })->name('salesman.customers');
    });

    Route::group(['prefix' => 'laravel-filemanager', 'middleware' => ['web', 'auth']], function () {
        Lfm::routes();
    });


    // Email Testing Routes
    Route::prefix('email-testing')->name('email-test.')->middleware(['auth', 'admin'])->group(function () {
        Route::get('/', [App\Http\Controllers\EmailTestController::class, 'index'])->name('index');
        Route::get('/preview/{template}', [App\Http\Controllers\EmailTestController::class, 'preview'])->name('preview');
        Route::post('/send-test', [App\Http\Controllers\EmailTestController::class, 'sendTest'])->name('send');
    });

    // Queue Monitoring Routes
    Route::prefix('queue-monitor')->name('queue-monitor.')->middleware(['auth', 'admin'])->group(function () {
        Route::get('/', [App\Http\Controllers\QueueMonitorController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\QueueMonitorController::class, 'getStats'])->name('stats');
        Route::get('/failed-jobs', [App\Http\Controllers\QueueMonitorController::class, 'getFailedJobs'])->name('failed-jobs');
        Route::get('/worker-status', [App\Http\Controllers\QueueMonitorController::class, 'getWorkerStatus'])->name('worker-status');
        Route::get('/health', [App\Http\Controllers\QueueMonitorController::class, 'healthCheck'])->name('health');
        Route::post('/retry-job/{id}', [App\Http\Controllers\QueueMonitorController::class, 'retryFailedJob'])->name('retry-job');
        Route::delete('/delete-job/{id}', [App\Http\Controllers\QueueMonitorController::class, 'deleteFailedJob'])->name('delete-job');
        Route::post('/flush-failed', [App\Http\Controllers\QueueMonitorController::class, 'flushFailedJobs'])->name('flush-failed');
        Route::post('/clear-queue', [App\Http\Controllers\QueueMonitorController::class, 'clearQueue'])->name('clear-queue');
    });

    // Legacy preview route (kept for backward compatibility)
    Route::get('/preview-email', function () {
        $order = \App\Models\Order::latest()->first();

        $data = [
            'locale' => App::getLocale(),
            'subject' => 'New Order Details',
            'order' => $order,
        ];

        return view('backend.emails.order_details_customer', $data);
    });
