@extends('backend.layouts.master')

@section('main-content')
<div class="card">
    <h5 class="card-header">Edit Price Level List</h5>
    <div class="card-body">
        <form method="post" action="{{route('list.update', $list->id)}}">
            @csrf
            @method('PATCH')

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="inputTitle" class="col-form-label">Price Level Name <span class="text-danger">*</span></label>
                        <input id="inputTitle" type="text" name="title" placeholder="Enter price level name" value="{{$list->title}}" class="form-control">
                        @error('title')
                        <span class="text-danger">{{$message}}</span>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="products">Select Products <span class="text-danger">*</span></label>
                <select name="products[]" id="products" class="form-control select2" multiple required>
                    @foreach($products as $product)
                        @if($product->item_colors && $product->item_colors->count() > 0)
                            @foreach($product->item_colors as $itemColor)
                                @php
                                    $key = $product->id . '-' . $itemColor->color->id;
                                    $isSelected = $item_price_level_lists->contains(function ($item) use ($product, $itemColor) {
                                        return $item->product_id == $product->id && $item->color_id == $itemColor->color->id;
                                    });
                                @endphp
                                <option value="{{ $key }}"
                                        data-item-number="{{ $itemColor->item_number }}"
                                        data-item-name="{{ $product->title }}"
                                        data-price="{{ $itemColor->price ?? $itemColor->price }}"
                                        data-color="{{ $itemColor->color->id }}"
                                        {{ $isSelected ? 'selected' : '' }}>
                                    {{ $itemColor->item_number }} - {{ $product->title }} ({{ $itemColor->color->name }})
                                </option>
                            @endforeach
                        @endif
                    @endforeach
                </select>
                @error('products')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="inactive" id="inactive" value="1" {{$list->status == 'inactive' ? 'checked' : ''}}>
                    <label class="form-check-label" for="inactive">
                        Price Level is inactive
                    </label>
                </div>
            </div>

            <div class="card mt-4" id="pricing-table-card" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Product Pricing</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm" id="product-pricing-table">
                        <thead>
                            <tr>
                                <th width="10%">ITEM</th>
                                <th width="40%">SKU</th>
                                <th width="25%">STANDARD PRICE</th>
                                <th width="25%">CUSTOM PRICE</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            @if($users->count() > 0 && $user_price_level_lists->count() > 0)
            <div class="card mt-4">
                <h5 class="card-header">Assigned Users</h5>
                <div class="card-body">
                    <table class="table table-bordered" id="users-details-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($users as $user)
                                @if($user_price_level_lists->contains('user_id', $user->id))
                                    <tr>
                                        <td>{{ $user->first_name . ' ' . $user->last_name }}</td>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            <div class="form-group mt-4">
                <button class="btn btn-success" type="submit">Update</button>
                <a href="{{ route('list.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('backend/js/select2.min.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'Select products',
        allowClear: true
    });

    // Store existing custom prices
    let existingPricesByKey = {};
    @foreach($item_price_level_lists as $item)
        @if($item->color_id)
            existingPricesByKey['{{ $item->product_id }}-{{ $item->color_id }}'] = {{ $item->custom_price }};
        @else
            existingPricesByKey['{{ $item->product_id }}'] = {{ $item->custom_price }};
        @endif
    @endforeach

    // Load table on page load
    updatePricingTable();

    // Update pricing table when products are selected/deselected
    $('#products').on('change', function() {
        updatePricingTable();
    });

    function updatePricingTable() {
        let selectedProducts = $('#products').val();
        let tbody = $('#product-pricing-table tbody');

        // Clear existing rows
        tbody.empty();

        if (!selectedProducts || selectedProducts.length === 0) {
            $('#pricing-table-card').hide();
            return;
        }

        $('#pricing-table-card').show();

        // Add rows for each selected product
        selectedProducts.forEach(function(productKey) {
            let option = $('#products option[value="' + productKey + '"]');
            let itemNumber = option.data('item-number');
            let sku = option.data('item-name');
            let standardPrice = parseFloat(option.data('price'));

            // Get existing custom price or use standard price
            let customPrice = existingPricesByKey[productKey] || standardPrice;

            let row = `
                <tr>
                    <td>${itemNumber}</td>
                    <td>${sku}</td>
                    <td>${standardPrice.toFixed(2)}</td>
                    <td>
                        <input type="number"
                               name="custom_prices[${productKey}]"
                               class="form-control form-control-sm custom-price"
                               step="0.01"
                               value="${customPrice.toFixed(2)}"
                               data-product-key="${productKey}"
                               required>
                    </td>
                </tr>
            `;

            tbody.append(row);
        });
    }

    // Form validation
    $('form').on('submit', function(e) {
        let title = $('#inputTitle').val().trim();
        let products = $('#products').val();

        if (!title) {
            e.preventDefault();
            Swal.fire('Error', 'Price Level Name is required', 'error');
            return false;
        }

        if (!products || products.length === 0) {
            e.preventDefault();
            Swal.fire('Error', 'Please select at least one product', 'error');
            return false;
        }

        // Validate all custom prices are filled
        let invalidPrices = false;
        $('.custom-price').each(function() {
            if (!$(this).val() || parseFloat($(this).val()) < 0) {
                invalidPrices = true;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (invalidPrices) {
            e.preventDefault();
            Swal.fire('Error', 'Please enter valid custom prices for all selected products', 'error');
            return false;
        }
    });
});
</script>
@endpush
