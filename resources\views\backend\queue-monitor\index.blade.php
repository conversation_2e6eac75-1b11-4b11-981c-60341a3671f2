@extends('backend.layouts.master')

@section('title', 'Queue Monitor')

@section('main-content')
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Queue Monitor</h1>
    <div>
        <button class="btn btn-sm btn-success" id="refresh-stats">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
        <button class="btn btn-sm btn-info" id="health-check">
            <i class="fas fa-heartbeat"></i> Health Check
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Pending Jobs</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-jobs">{{ $stats['pending_jobs'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Jobs Today</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="jobs-today">{{ $stats['jobs_today'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Failed Jobs</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="failed-jobs">{{ $stats['failed_jobs'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Processing</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="avg-processing">{{ $stats['avg_processing_time'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tachometer-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Queue Status by Type -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pending Jobs by Queue</h6>
            </div>
            <div class="card-body">
                <div id="queue-breakdown">
                    @if(empty($stats['pending_by_queue']))
                        <p class="text-muted">No pending jobs</p>
                    @else
                        @foreach($stats['pending_by_queue'] as $queue => $count)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="font-weight-bold">{{ ucfirst($queue) }}</span>
                            <span class="badge badge-primary">{{ $count }}</span>
                        </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Worker Status</h6>
                <button class="btn btn-sm btn-outline-primary" id="check-workers">
                    <i class="fas fa-sync-alt"></i> Check
                </button>
            </div>
            <div class="card-body">
                <div id="worker-status">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Checking workers...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Failed Jobs -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Failed Jobs</h6>
        <div>
            <button class="btn btn-sm btn-warning" id="retry-all-failed">
                <i class="fas fa-redo"></i> Retry All
            </button>
            <button class="btn btn-sm btn-danger" id="flush-failed">
                <i class="fas fa-trash"></i> Delete All
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="failed-jobs-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Job Class</th>
                        <th>Queue</th>
                        <th>Failed At</th>
                        <th>Exception</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($failedJobs as $job)
                    <tr>
                        <td>{{ $job['id'] }}</td>
                        <td>{{ $job['job_class'] }}</td>
                        <td><span class="badge badge-secondary">{{ $job['queue'] }}</span></td>
                        <td>{{ $job['failed_at']->format('Y-m-d H:i:s') }}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="showException('{{ $job['id'] }}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-success retry-job" data-id="{{ $job['id'] }}">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                            <button class="btn btn-sm btn-danger delete-job" data-id="{{ $job['id'] }}">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center text-muted">No failed jobs</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Exception Modal -->
<div class="modal fade" id="exceptionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Exception Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="exception-content" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-refresh every 30 seconds
    setInterval(refreshStats, 30000);
    
    // Check worker status on load
    checkWorkerStatus();

    // Refresh stats
    $('#refresh-stats').click(refreshStats);
    
    // Health check
    $('#health-check').click(function() {
        $.get('{{ route("queue-monitor.health") }}')
            .done(function(data) {
                let alertClass = data.status === 'healthy' ? 'success' : 
                               data.status === 'warning' ? 'warning' : 'danger';
                let message = data.status === 'healthy' ? 'System is healthy' : 
                             'Issues found: ' + data.issues.join(', ');
                
                showAlert(message, alertClass);
            })
            .fail(function() {
                showAlert('Failed to check system health', 'danger');
            });
    });

    // Check workers
    $('#check-workers').click(checkWorkerStatus);

    // Retry single job
    $(document).on('click', '.retry-job', function() {
        const jobId = $(this).data('id');
        retryJob(jobId);
    });

    // Delete single job
    $(document).on('click', '.delete-job', function() {
        const jobId = $(this).data('id');
        if (confirm('Are you sure you want to delete this failed job?')) {
            deleteJob(jobId);
        }
    });

    // Retry all failed jobs
    $('#retry-all-failed').click(function() {
        if (confirm('Are you sure you want to retry all failed jobs?')) {
            // Implementation would depend on your backend API
            showAlert('Feature not implemented yet', 'info');
        }
    });

    // Flush all failed jobs
    $('#flush-failed').click(function() {
        if (confirm('Are you sure you want to delete ALL failed jobs? This cannot be undone.')) {
            $.post('{{ route("queue-monitor.flush-failed") }}', {
                _token: '{{ csrf_token() }}'
            })
            .done(function(data) {
                if (data.success) {
                    showAlert(data.message, 'success');
                    location.reload();
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .fail(function() {
                showAlert('Failed to flush failed jobs', 'danger');
            });
        }
    });
});

function refreshStats() {
    $.get('{{ route("queue-monitor.stats") }}')
        .done(function(data) {
            $('#pending-jobs').text(data.pending_jobs);
            $('#jobs-today').text(data.jobs_today);
            $('#failed-jobs').text(data.failed_jobs);
            $('#avg-processing').text(data.avg_processing_time);
            
            // Update queue breakdown
            let breakdown = '';
            if (Object.keys(data.pending_by_queue).length === 0) {
                breakdown = '<p class="text-muted">No pending jobs</p>';
            } else {
                for (let queue in data.pending_by_queue) {
                    breakdown += `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="font-weight-bold">${queue.charAt(0).toUpperCase() + queue.slice(1)}</span>
                            <span class="badge badge-primary">${data.pending_by_queue[queue]}</span>
                        </div>
                    `;
                }
            }
            $('#queue-breakdown').html(breakdown);
        })
        .fail(function() {
            showAlert('Failed to refresh stats', 'danger');
        });
}

function checkWorkerStatus() {
    $('#worker-status').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Checking workers...</div>');
    
    $.get('{{ route("queue-monitor.worker-status") }}')
        .done(function(data) {
            let status = '';
            if (data.workers_running) {
                status = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> ${data.worker_count} worker(s) running
                    </div>
                `;
            } else {
                status = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> No workers detected
                    </div>
                `;
            }
            $('#worker-status').html(status);
        })
        .fail(function() {
            $('#worker-status').html('<div class="alert alert-danger">Failed to check worker status</div>');
        });
}

function retryJob(jobId) {
    $.post(`{{ route("queue-monitor.retry-job", ":id") }}`.replace(':id', jobId), {
        _token: '{{ csrf_token() }}'
    })
    .done(function(data) {
        if (data.success) {
            showAlert(data.message, 'success');
            location.reload();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to retry job', 'danger');
    });
}

function deleteJob(jobId) {
    $.ajax({
        url: `{{ route("queue-monitor.delete-job", ":id") }}`.replace(':id', jobId),
        method: 'DELETE',
        data: {
            _token: '{{ csrf_token() }}'
        }
    })
    .done(function(data) {
        if (data.success) {
            showAlert(data.message, 'success');
            location.reload();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('Failed to delete job', 'danger');
    });
}

function showException(jobId) {
    // This would fetch and display the exception details
    $('#exception-content').text('Exception details for job ' + jobId + ' would be shown here.');
    $('#exceptionModal').modal('show');
}

function showAlert(message, type) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top
    $('main').prepend(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
@endpush
