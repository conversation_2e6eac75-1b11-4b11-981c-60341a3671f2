<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('price_level_lists', function (Blueprint $table) {
            $table->dropColumn(['price_type', 'price_value', 'method']);
            $table->unique('title');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('price_level_lists', function (Blueprint $table) {
            $table->dropUnique(['title']);
            $table->enum('price_type', ['fixed', 'percentage'])->default('fixed')->after('title');
            $table->decimal('price_value', 8, 2)->default(0)->after('price_type');
            $table->enum('method', ['plus', 'minus'])->default('plus')->after('price_value');
        });
    }
};
